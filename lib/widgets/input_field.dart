import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shopping/constants/app_colors.dart';

class InputField extends StatelessWidget {
  final TextEditingController? controller;
  FocusNode? focusNode;
  final TextInputType keyboardType;
  final String labelText;
  final String hintText;
  final Color color;
  final Color colorHint;
  final double fontSize;
  bool password;
  final String? Function(String?)? validator;
  int? limit = 100;


  InputField({
    this.controller,
    this.focusNode,
    this.keyboardType = TextInputType.text,
    this.labelText = '',
    this.hintText = '',
    this.colorHint = AppColors.grey6A6A6A,
    this.color = Colors.black,
    this.fontSize = 14.0,
    this.password = false,
    this.limit,

    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      focusNode: focusNode,

      inputFormatters: [
        LengthLimitingTextInputFormatter(limit),
      ],
      decoration: InputDecoration(
        fillColor: Colors.transparent,
        contentPadding: const EdgeInsets.only(top: 15, bottom: 15),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: color,
          ),
        ),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: colorHint,
          ),
        ),
        floatingLabelBehavior: FloatingLabelBehavior.always,
        labelText: labelText,
        labelStyle: TextStyle(
          fontSize: fontSize - 2,
          color: color,
          height: 0.2,
          fontWeight: FontWeight.normal,
        ),
        hintText: hintText,
        hintStyle: TextStyle(
          fontSize: fontSize,
          color: colorHint,
          fontWeight: FontWeight.normal,
        ),
        filled: true,
        isDense: true,
      ),
      controller: controller,
      style: TextStyle(
        color: color,
        fontSize: fontSize,
        fontWeight: FontWeight.normal,
      ),
      keyboardType: keyboardType,
      obscureText: password,
      autocorrect: false,
      validator: validator,
    );
  }
}
