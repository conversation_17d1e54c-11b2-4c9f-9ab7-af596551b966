import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:shopping/constants/app_assets_paths.dart';
import 'package:shopping/constants/app_colors.dart';
import 'package:shopping/constants/app_text_style.dart';
import 'package:shopping/loading_screen.dart';
import 'package:shopping/tabs/profile/profile_controller.dart';
import 'package:shopping/tabs/profile/widgets/profile_avatar_widget.dart';

import '../../enum/sex_enum.dart';
import 'edit_profile_screen.dart';

class ProfileTab extends StatelessWidget {
  late ProfileController controller;
  var isShowNavigatorBar = false;
  ProfileTab(
    this.isShowNavigatorBar, {
    Key? key,
  }) : super(key: key) {}
  @override
  Widget build(BuildContext context) {
    return profileOrderWidget(context);
  }

  Widget profileOrderWidget(BuildContext context) {
    return GetBuilder<ProfileController>(
      init: ProfileController(),
      builder: (controller) => LoadingOverlay(
        child: Scaffold(
          backgroundColor: AppColors.white,
          appBar: AppBar(
            automaticallyImplyLeading: false,
            centerTitle: true,
            backgroundColor: AppColors.white,
            elevation: 5,
            shadowColor: AppColors.black.withOpacity(0.4),
            surfaceTintColor: AppColors.white,
            scrolledUnderElevation: 0,
            title: const Text(
              "TÀI KHOẢN",
            ),
            titleTextStyle: AppTextStyle.s16Bold.copyWith(color: Colors.black),
            actions: [
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => EditProfileScreen()));
                },
                child: Container(
                  margin: EdgeInsets.only(right: 17),
                  child: SvgPicture.asset(
                    SvgPath.svgIconEditProfile,
                    height: 20,
                    width: 20,
                  ),
                ),
              ),
            ],
          ),
          body: Container(
            height: MediaQuery.sizeOf(context).height,
            width: MediaQuery.sizeOf(context).width,
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  offset: Offset(0, -650),
                  blurRadius: 20,
                  color: Color(0xffc8c8c8).withOpacity(0.2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.only(bottom: 10, left: 15, right: 15),
              child: Column(
                children: [
                  Flexible(
                    child: SingleChildScrollView(
                      physics: ClampingScrollPhysics(),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            height: 15,
                          ),
                          ProfileAvatarWidget(
                            path: controller.user?.imgAvatar,
                            onChangeAvt: (avt) {
                              if (avt == null) {
                                return;
                              }
                              controller.pickAvatar(context, avt);
                            },
                            name: controller.user?.fullname,
                          ),
                          SizedBox(
                            height: 34,
                          ),
                          infoProfileWidget(
                              label: 'Họ và tên',
                              value: controller.user?.fullname),
                          SizedBox(
                            height: 8,
                          ),
                          infoProfileWidget(
                              label: 'Số điện thoại',
                              value: controller.user?.phoneNumber),
                          SizedBox(
                            height: 8,
                          ),
                          infoProfileWidget(
                              label: 'Email', value: controller.user?.email),
                          SizedBox(
                            height: 8,
                          ),
                          infoProfileWidget(
                              label: 'Ngày sinh',
                              value: controller.user?.birthday),
                          SizedBox(
                            height: 8,
                          ),
                          infoProfileWidget(
                              label: 'Giới tính',
                              value: SexEnum.fromId(controller.user?.sex).name),
                          SizedBox(
                            height: 8,
                          ),
                          infoProfileWidget(
                              label: 'CCCD/CMND',
                              value: controller.user?.cccd.toString()),
                          SizedBox(
                            height: 8,
                          ),
                          infoProfileWidget(
                              label: 'Địa chỉ',
                              value: controller.user?.addressFull),
                          SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: Center(
                      child: Text(
                        'Hotline CSKH: 0886901166 hoặc 0902145511',
                        textAlign: TextAlign.center,
                        style: AppTextStyle.s12Medium
                            .copyWith(color: AppColors.black),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget infoProfileWidget({String? label, String? value, bool? isDate}) {
    return Container(
      decoration: BoxDecoration(
          color: AppColors.greyF5F6F9, borderRadius: BorderRadius.circular(10)),
      padding: const EdgeInsets.all(15),
      margin: EdgeInsets.symmetric(horizontal: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label ?? '',
              style: AppTextStyle.s14Medium
                  .copyWith(color: AppColors.black, height: 17 / 12)),
          SizedBox(
            width: 10,
          ),
          Expanded(
            child: Align(
              alignment: FractionalOffset.centerRight,
              child: Text(
                textAlign: TextAlign.right,
                (value != 'null' && value != '')
                    ? value ?? 'Không có dữ liệu'
                    : 'Không có dữ liệu',
                style: AppTextStyle.s14SemiBold.copyWith(
                  color: AppColors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
